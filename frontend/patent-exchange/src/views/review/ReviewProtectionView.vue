<template>
  <div class="review-protection-view">
    <div class="container-fluid py-4">
      <!-- Header -->
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-shield-check text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">维权审核</h2>
          <p class="text-muted mb-0">审核专利维权申请</p>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card border-0 bg-warning text-dark">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">待审核</h6>
                  <h3 class="mb-0">{{ pendingRequests.length }}</h3>
                </div>
                <i class="bi bi-clock-history fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">已批准</h6>
                  <h3 class="mb-0">{{ approvedCount }}</h3>
                </div>
                <i class="bi bi-check-circle fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-danger text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">已拒绝</h6>
                  <h3 class="mb-0">{{ rejectedCount }}</h3>
                </div>
                <i class="bi bi-x-circle fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card border-0 bg-info text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1">总计</h6>
                  <h3 class="mb-0">{{ totalCount }}</h3>
                </div>
                <i class="bi bi-bar-chart fs-1 opacity-75"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Alert -->
      <div v-if="error" class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
        <button type="button" class="btn-close" @click="error = null"></button>
      </div>

      <!-- Success Alert -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="text-muted mt-3">正在加载待审核维权申请...</p>
      </div>

      <!-- Pending Protection Requests List -->
      <div v-else-if="pendingRequests.length > 0" class="row">
        <div v-for="request in pendingRequests" :key="request.id" class="col-12 mb-4">
          <div class="card shadow-sm">
            <div class="card-header bg-light">
              <div class="row align-items-center">
                <div class="col">
                  <h5 class="mb-1">
                    <i class="bi bi-shield-exclamation text-warning me-2"></i>
                    专利维权申请
                  </h5>
                  <small class="text-muted">申请ID: {{ request.id }}</small>
                </div>
                <div class="col-auto">
                  <span class="badge bg-warning">待审核</span>
                </div>
              </div>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Request Information -->
                <div class="col-md-6">
                  <h6 class="text-primary mb-3">
                    <i class="bi bi-info-circle me-2"></i>
                    申请信息
                  </h6>
                  <div class="mb-2">
                    <strong>专利区块链地址:</strong>
                    <a
                      href="#"
                      class="text-decoration-none ms-2"
                      @click.prevent="showPatentDetails(request.patentAddress)"
                    >
                      {{ formatAddress(request.patentAddress) }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </a>
                  </div>
                  <div class="mb-2">
                    <strong>专利名称:</strong>
                    <span class="ms-2">{{ request.patentName }}</span>
                  </div>
                  <div class="mb-2">
                    <strong>申请人:</strong>
                    <a
                      href="#"
                      class="text-decoration-none ms-2"
                      @click.prevent="showUserDetails(request.applicantAddress)"
                    >
                      {{ request.applicantName }}
                      <small class="text-muted">({{ formatAddress(request.applicantAddress) }})</small>
                    </a>
                  </div>
                  <div class="mb-2">
                    <strong>提交时间:</strong>
                    <span class="ms-2">{{ formatDate(request.submitDate) }}</span>
                  </div>
                </div>

                <!-- Description -->
                <div class="col-md-6">
                  <h6 class="text-primary mb-3">
                    <i class="bi bi-file-text me-2"></i>
                    维权描述
                  </h6>
                  <div class="bg-light p-3 rounded">
                    <p class="mb-0 text-muted">
                      {{ request.description }}
                    </p>
                    <button
                      v-if="request.description.length > 150"
                      class="btn btn-link btn-sm p-0 mt-2"
                      @click="showDescriptionModal(request)"
                    >
                      查看完整描述
                    </button>
                  </div>
                </div>
              </div>

              <!-- Evidence Document -->
              <div class="mt-3">
                <h6 class="text-primary mb-2">
                  <i class="bi bi-paperclip me-2"></i>
                  证明文档
                </h6>
                <div class="d-flex gap-2">
                  <a
                    :href="request.evidenceUrl"
                    target="_blank"
                    class="btn btn-outline-primary btn-sm"
                  >
                    <i class="bi bi-file-earmark-pdf me-2"></i>
                    查看证明文档
                  </a>
                  <a
                    :href="request.evidenceUrl"
                    download
                    class="btn btn-outline-secondary btn-sm"
                  >
                    <i class="bi bi-download me-2"></i>
                    下载文档
                  </a>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="mt-4 d-flex gap-2">
                <button
                  class="btn btn-success flex-fill"
                  @click="approveRequest(request)"
                  :disabled="isProcessing"
                >
                  <span v-if="isProcessing && processingId === request.id" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-check-lg me-2"></i>
                  批准
                </button>
                <button
                  class="btn btn-danger flex-fill"
                  @click="showRejectModal(request)"
                  :disabled="isProcessing"
                >
                  <i class="bi bi-x-lg me-2"></i>
                  拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-5">
        <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
        <h4 class="text-muted mt-3">暂无待审核维权申请</h4>
        <p class="text-muted">所有维权申请都已处理完毕</p>
      </div>
    </div>

    <!-- Description Modal -->
    <div
      class="modal fade"
      id="descriptionModal"
      tabindex="-1"
      aria-labelledby="descriptionModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-info text-white">
            <h5 class="modal-title" id="descriptionModalLabel">
              <i class="bi bi-file-text me-2"></i>
              完整维权描述
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedRequest" class="mb-3">
              <h6 class="text-muted">申请信息</h6>
              <p class="mb-1"><strong>{{ selectedRequest.patentName }}</strong></p>
              <small class="text-muted">申请人: {{ selectedRequest.applicantName }}</small>
            </div>
            <div v-if="selectedRequest">
              <h6 class="text-muted mb-3">维权描述详情</h6>
              <div class="bg-light p-3 rounded">
                <p class="mb-0" style="white-space: pre-wrap;">{{ selectedRequest.description }}</p>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-lg me-2"></i>
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Patent Details Modal -->
    <div
      class="modal fade"
      id="patentDetailsModal"
      tabindex="-1"
      aria-labelledby="patentDetailsModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-primary text-white">
            <h5 class="modal-title" id="patentDetailsModalLabel">
              <i class="bi bi-file-earmark-text me-2"></i>
              专利区块链信息
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedPatentAddress" class="text-center py-4">
              <i class="bi bi-link-45deg text-primary" style="font-size: 3rem;"></i>
              <h5 class="mt-3">区块链地址</h5>
              <div class="input-group mt-3">
                <input
                  type="text"
                  class="form-control font-monospace"
                  :value="selectedPatentAddress"
                  readonly
                  style="background-color: #f8f9fa;"
                >
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  @click="copyPatentAddress"
                  :title="copyButtonText"
                >
                  <i :class="copyIcon"></i>
                </button>
              </div>
              <div class="alert alert-info mt-3" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <small>此地址对应区块链上的专利记录，可用于查询详细的专利信息</small>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-lg me-2"></i>
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div
      class="modal fade"
      id="rejectModal"
      tabindex="-1"
      aria-labelledby="rejectModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="rejectModalLabel">
              <i class="bi bi-x-circle me-2"></i>
              拒绝维权申请
            </h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedRequest" class="mb-3">
              <h6 class="text-muted">申请信息</h6>
              <p class="mb-1"><strong>{{ selectedRequest.patentName }}</strong></p>
              <small class="text-muted">申请人: {{ selectedRequest.applicantName }}</small>
            </div>

            <div class="mb-3">
              <label for="rejectReason" class="form-label">
                拒绝原因 <span class="text-danger">*</span>
              </label>
              <textarea
                id="rejectReason"
                class="form-control"
                rows="4"
                v-model="rejectReason"
                placeholder="请详细说明拒绝原因..."
                :class="{ 'is-invalid': rejectReasonError }"
              ></textarea>
              <div v-if="rejectReasonError" class="invalid-feedback">
                {{ rejectReasonError }}
              </div>
            </div>

            <!-- Quick Reason Templates -->
            <div class="mb-3">
              <label class="form-label">常用拒绝原因</label>
              <div class="d-flex flex-wrap gap-2">
                <button
                  v-for="template in rejectTemplates"
                  :key="template"
                  type="button"
                  class="btn btn-outline-secondary btn-sm"
                  @click="rejectReason = template"
                >
                  {{ template }}
                </button>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-lg me-2"></i>
              取消
            </button>
            <button
              type="button"
              class="btn btn-danger"
              @click="confirmReject"
              :disabled="isProcessing"
            >
              <span v-if="isProcessing" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else class="bi bi-check-lg me-2"></i>
              确认拒绝
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { transactionService } from '@/services/transactionService'
import UserDetailModal from '@/components/UserDetailModal.vue'

export default {
  name: 'ReviewProtectionView',
  components: {
    UserDetailModal
  },
  setup() {
    const authStore = useAuthStore()

    // State
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const processingId = ref(null)
    const error = ref(null)
    const successMessage = ref(null)
    const pendingRequests = ref([])
    const selectedRequest = ref(null)
    const selectedUserAddress = ref(null)
    const selectedPatentAddress = ref(null)

    // Reject modal state
    const rejectReason = ref('')
    const rejectReasonError = ref(null)

    // Copy functionality
    const copyButtonText = ref('复制地址')
    const copyIcon = ref('bi bi-clipboard')

    // Statistics
    const approvedCount = ref(0)
    const rejectedCount = ref(0)
    const totalCount = computed(() =>
      pendingRequests.value.length + approvedCount.value + rejectedCount.value
    )

    // Reject reason templates
    const rejectTemplates = [
      '证据不足，无法证明侵权行为',
      '维权申请信息不完整',
      '专利权归属存在争议',
      '申请超出维权范围',
      '证明文档无效或缺失',
      '不符合维权申请条件',
      '重复申请或已处理',
      '违反平台维权规则'
    ]

    // Load pending protection requests
    const loadPendingRequests = async () => {
      try {
        isLoading.value = true
        error.value = null

        const requests = await transactionService.getPendingProtectionRequests()
        pendingRequests.value = requests

        // Load real statistics from backend
        try {
          const stats = await transactionService.getReviewStatistics()
          approvedCount.value = stats.protection?.approved || 0
          rejectedCount.value = stats.protection?.rejected || 0
        } catch (statsError) {
          console.warn('Failed to load statistics:', statsError)
          // Fallback to basic counts if statistics fail
          approvedCount.value = 0
          rejectedCount.value = 0
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    // Approve protection request
    const approveRequest = async (request) => {
      try {
        isProcessing.value = true
        processingId.value = request.id

        const result = await transactionService.approveProtectionRequest(
          request.id,
          authStore.account
        )

        if (result.success) {
          successMessage.value = `维权申请已批准`
          // Remove from pending list
          pendingRequests.value = pendingRequests.value.filter(
            r => r.id !== request.id
          )
          approvedCount.value++
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isProcessing.value = false
        processingId.value = null
      }
    }

    // Show reject modal
    const showRejectModal = (request) => {
      selectedRequest.value = request
      rejectReason.value = ''
      rejectReasonError.value = null
      const modal = new bootstrap.Modal(document.getElementById('rejectModal'))
      modal.show()
    }

    // Confirm reject
    const confirmReject = async () => {
      try {
        if (!rejectReason.value.trim()) {
          rejectReasonError.value = '请输入拒绝原因'
          return
        }

        rejectReasonError.value = null
        isProcessing.value = true

        const result = await transactionService.rejectProtectionRequest(
          selectedRequest.value.id,
          authStore.account,
          rejectReason.value
        )

        if (result.success) {
          successMessage.value = `维权申请已拒绝`

          // Remove from pending list
          pendingRequests.value = pendingRequests.value.filter(
            r => r.id !== selectedRequest.value.id
          )
          rejectedCount.value++

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('rejectModal'))
          modal.hide()
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isProcessing.value = false
      }
    }

    // Show description modal
    const showDescriptionModal = (request) => {
      selectedRequest.value = request
      const modal = new bootstrap.Modal(document.getElementById('descriptionModal'))
      modal.show()
    }

    // Show patent details modal
    const showPatentDetails = (patentAddress) => {
      selectedPatentAddress.value = patentAddress
      const modal = new bootstrap.Modal(document.getElementById('patentDetailsModal'))
      modal.show()
    }

    // Show user details modal
    const showUserDetails = (address) => {
      selectedUserAddress.value = address
      const modal = new bootstrap.Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    // Copy patent address
    const copyPatentAddress = async () => {
      try {
        await navigator.clipboard.writeText(selectedPatentAddress.value)
        copyButtonText.value = '已复制!'
        copyIcon.value = 'bi bi-check'

        setTimeout(() => {
          copyButtonText.value = '复制地址'
          copyIcon.value = 'bi bi-clipboard'
        }, 2000)
      } catch (err) {
        console.error('复制失败:', err)
      }
    }

    // Utility functions
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    // Initialize
    onMounted(() => {
      if (authStore.isConnected) {
        loadPendingRequests()
      }
    })

    return {
      isLoading,
      isProcessing,
      processingId,
      error,
      successMessage,
      pendingRequests,
      selectedRequest,
      selectedUserAddress,
      selectedPatentAddress,
      rejectReason,
      rejectReasonError,
      rejectTemplates,
      copyButtonText,
      copyIcon,
      approvedCount,
      rejectedCount,
      totalCount,
      approveRequest,
      showRejectModal,
      confirmReject,
      showDescriptionModal,
      showPatentDetails,
      showUserDetails,
      copyPatentAddress,
      formatDate,
      formatAddress
    }
  }
}
</script>

<style scoped>
.review-protection-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.modal-content {
  border-radius: 12px;
}

.modal-header {
  border-radius: 12px 12px 0 0;
}

.badge {
  font-size: 0.75rem;
}

.text-primary {
  color: #0d6efd !important;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.font-monospace {
  font-family: 'Courier New', monospace;
}

.opacity-75 {
  opacity: 0.75;
}

.fs-1 {
  font-size: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.flex-fill {
  flex: 1 1 auto;
}

.flex-wrap {
  flex-wrap: wrap;
}

.text-warning {
  color: #ffc107 !important;
}

.bg-warning {
  background-color: #ffc107 !important;
}

.input-group .form-control {
  border-radius: 6px 0 0 6px;
}

.input-group .btn {
  border-radius: 0 6px 6px 0;
}
</style>
