import api from './apiClient.js'

// Transaction service for managing patent transactions and rights protection
export const transactionService = {
  // Get pending transactions for review (reviewer view)
  async getPendingTransactions() {
    try {
      const response = await api.transactions.getPending()
      return response.data.data
    } catch (error) {
      console.error('获取待审核交易失败:', error)
      throw new Error(error.message || '获取待审核交易失败')
    }
  },

  // Get user's transactions
  async getUserTransactions(userAddress, params = {}) {
    try {
      const response = await api.transactions.getUserTransactions(userAddress, params)
      return response.data.data.transactions || []
    } catch (error) {
      console.error('获取用户交易记录失败:', error)
      throw new Error(error.message || '获取用户交易记录失败')
    }
  },

  // Initiate patent transaction
  async initiateTransaction(transactionData) {
    try {
      const response = await api.transactions.initiate(transactionData)
      return response.data
    } catch (error) {
      console.error('发起交易失败:', error)
      throw new Error(error.message || '发起交易失败')
    }
  },

  // Approve transaction (reviewer action)
  async approveTransaction(transactionId, reviewerAddress, comments = '') {
    try {
      const response = await api.transactions.approve(transactionId, {
        reviewerAddress,
        comments
      })
      return response.data
    } catch (error) {
      console.error('批准交易失败:', error)
      throw new Error(error.message || '批准交易失败')
    }
  },

  // Reject transaction (reviewer action)
  async rejectTransaction(transactionId, reviewerAddress, reason, comments = '') {
    try {
      const response = await api.transactions.reject(transactionId, {
        reviewerAddress,
        reason,
        comments
      })
      return response.data
    } catch (error) {
      console.error('拒绝交易失败:', error)
      throw new Error(error.message || '拒绝交易失败')
    }
  },

  // Get pending patent uploads for review (reviewer view)
  async getPendingUploads() {
    try {
      const response = await api.review.getPendingUploads()
      return response.data.data
    } catch (error) {
      console.error('获取待审核上传失败:', error)
      throw new Error(error.message || '获取待审核上传失败')
    }
  },

  // Approve patent upload (reviewer action)
  async approveUpload(uploadId, reviewerAddress, comments = '') {
    try {
      const response = await api.review.approveUpload(uploadId, {
        reviewerAddress,
        comments
      })
      return response.data
    } catch (error) {
      console.error('批准上传失败:', error)
      throw new Error(error.message || '批准上传失败')
    }
  },

  // Reject patent upload (reviewer action)
  async rejectUpload(uploadId, reviewerAddress, reason, comments = '') {
    try {
      const response = await api.review.rejectUpload(uploadId, {
        reviewerAddress,
        reason,
        comments
      })
      return response.data
    } catch (error) {
      console.error('拒绝上传失败:', error)
      throw new Error(error.message || '拒绝上传失败')
    }
  },

  // Get pending rights protection requests for review (reviewer view)
  async getPendingProtectionRequests() {
    try {
      const response = await api.protection.getPending()
      return response.data.data
    } catch (error) {
      console.error('获取待审核维权申请失败:', error)
      throw new Error(error.message || '获取待审核维权申请失败')
    }
  },

  // Approve rights protection request (reviewer action)
  async approveProtectionRequest(requestId, reviewerAddress, resolution = '') {
    try {
      const response = await api.protection.approve(requestId, {
        reviewerAddress,
        resolution
      })
      return response.data
    } catch (error) {
      console.error('批准维权申请失败:', error)
      throw new Error(error.message || '批准维权申请失败')
    }
  },

  // Reject rights protection request (reviewer action)
  async rejectProtectionRequest(requestId, reviewerAddress, reason) {
    try {
      const response = await api.protection.reject(requestId, {
        reviewerAddress,
        reason
      })
      return response.data
    } catch (error) {
      console.error('拒绝维权申请失败:', error)
      throw new Error(error.message || '拒绝维权申请失败')
    }
  },

  // Get pending rights protection cases
  async getPendingProtectionCases() {
    try {
      const response = await api.protection.getCases()
      return response.data.data
    } catch (error) {
      console.error('获取待审核维权案例失败:', error)
      throw new Error(error.message || '获取待审核维权案例失败')
    }
  },

  // Initiate rights protection
  async initiateRightsProtection(protectionData) {
    try {
      const response = await api.protection.request(protectionData)
      return response.data
    } catch (error) {
      console.error('发起维权失败:', error)
      throw new Error(error.message || '发起维权失败')
    }
  },

  // Approve rights protection (reviewer action)
  async approveRightsProtection(caseId, reviewerAddress, resolution = '') {
    try {
      const response = await api.protection.approve(caseId, {
        reviewerAddress,
        resolution
      })
      return response.data
    } catch (error) {
      console.error('批准维权失败:', error)
      throw new Error(error.message || '批准维权失败')
    }
  },

  // Reject rights protection (reviewer action)
  async rejectRightsProtection(caseId, reviewerAddress, reason) {
    try {
      const response = await api.protection.reject(caseId, {
        reviewerAddress,
        reason
      })
      return response.data
    } catch (error) {
      console.error('拒绝维权失败:', error)
      throw new Error(error.message || '拒绝维权失败')
    }
  },

  // Get review statistics
  async getReviewStatistics() {
    try {
      const response = await api.review.getStatistics()
      return response.data
    } catch (error) {
      console.error('获取审核统计失败:', error)
      throw new Error(error.message || '获取审核统计失败')
    }
  }
}
