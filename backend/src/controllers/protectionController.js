const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Protection Controller
 * Handles rights protection operations
 */

/**
 * Submit a rights protection request
 */
const submitProtectionRequest = async (req, res) => {
  try {
    const { patentAddress, patentName, applicantAddress, description, evidenceFiles } = req.body;

    // Validate inputs
    if (!patentAddress || !patentName || !description) {
      throw new ValidationError('Patent address, patent name, and description are required');
    }

    if (!blockchainService.web3.utils.isAddress(patentAddress)) {
      throw new ValidationError('Invalid patent address format');
    }

    // For now, we'll use a simple approach where patentAddress is the patent ID
    // In a real implementation, you might need to resolve the address to a patent ID
    const patentId = parseInt(patentAddress.slice(-2), 16); // Simple conversion for demo

    // Verify patent exists
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== patentId.toString()) {
      throw new NotFoundError('Patent not found');
    }

    // Submit protection request to blockchain
    const result = await blockchainService.callContractMethod(
      'ProtectionManager',
      'submitProtectionRequest',
      [patentId, patent.uploaderAddress, description, evidenceFiles ? evidenceFiles.join(',') : ''],
      { send: true, from: req.userAddress }
    );

    // Extract protection case ID from events
    let protectionId = null;
    if (result.events && result.events.ProtectionRequested) {
      protectionId = result.events.ProtectionRequested.returnValues.caseId;
    }

    res.created({
      protectionId: protectionId || 'pending',
      status: 'pending',
      transactionHash: result.transactionHash,
      message: '维权申请已提交'
    }, '维权申请已提交');

  } catch (error) {
    console.error('Error submitting protection request:', error);
    throw error;
  }
};

/**
 * Get pending protection requests
 */
const getPendingProtectionRequests = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending protection requests
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending protection requests');
    }

    // Get pending protection cases from blockchain
    const pendingCases = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getPendingProtectionCases',
      [],
      { from: req.userAddress }
    );

    const protectionRequests = [];

    // Get details for each pending case
    for (const caseId of pendingCases) {
      try {
        const protectionCase = await blockchainService.callContractMethod(
          'ProtectionManager',
          'getProtectionCase',
          [caseId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [protectionCase.patentId]
        );

        // Get applicant profile
        let applicantName = 'Unknown';
        try {
          const applicantProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [protectionCase.claimantAddress]
          );
          applicantName = applicantProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get applicant profile:', error);
        }

        protectionRequests.push({
          id: protectionCase.id.toString(),
          patentAddress: formatAddress(patent.uploaderAddress),
          patentName: patent.name,
          applicantAddress: formatAddress(protectionCase.claimantAddress),
          applicantName,
          description: protectionCase.description,
          submitDate: formatDate(new Date(parseInt(protectionCase.submitDate) * 1000)),
          evidenceUrl: protectionCase.evidenceHash ? `/documents/evidence_${protectionCase.id}.pdf` : null,
          status: 'pending'
        });
      } catch (error) {
        console.warn(`Failed to get protection case details for ID ${caseId}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    protectionRequests.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    res.success({ data: protectionRequests });

  } catch (error) {
    console.error('Error getting pending protection requests:', error);
    throw error;
  }
};

/**
 * Approve a protection request
 */
const approveProtectionRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, resolution } = req.body;
    const caseId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can approve protection requests');
    }

    if (isNaN(caseId) || caseId < 0) {
      throw new ValidationError('Invalid case ID');
    }

    if (!resolution) {
      throw new ValidationError('Resolution is required');
    }

    // Verify protection case exists
    const protectionCase = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getProtectionCase',
      [caseId]
    );

    if (!protectionCase || protectionCase.id.toString() !== id) {
      throw new NotFoundError('Protection case not found');
    }

    // Check if case is pending
    if (protectionCase.status !== 0) { // 0 = pending
      throw new ValidationError('Protection case is not pending review');
    }

    // Approve protection request on blockchain
    const result = await blockchainService.callContractMethod(
      'ProtectionManager',
      'approveProtection',
      [caseId, resolution],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      caseId: id,
      message: '维权申请已批准'
    }, '维权申请已批准');

  } catch (error) {
    console.error('Error approving protection request:', error);
    throw error;
  }
};

/**
 * Reject a protection request
 */
const rejectProtectionRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, reason } = req.body;
    const caseId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can reject protection requests');
    }

    if (isNaN(caseId) || caseId < 0) {
      throw new ValidationError('Invalid case ID');
    }

    if (!reason) {
      throw new ValidationError('Rejection reason is required');
    }

    // Verify protection case exists
    const protectionCase = await blockchainService.callContractMethod(
      'ProtectionManager',
      'getProtectionCase',
      [caseId]
    );

    if (!protectionCase || protectionCase.id.toString() !== id) {
      throw new NotFoundError('Protection case not found');
    }

    // Check if case is pending
    if (protectionCase.status !== 0) { // 0 = pending
      throw new ValidationError('Protection case is not pending review');
    }

    // Reject protection request on blockchain
    const result = await blockchainService.callContractMethod(
      'ProtectionManager',
      'rejectProtection',
      [caseId, reason],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      caseId: id,
      message: '维权申请已拒绝'
    }, '维权申请已拒绝');

  } catch (error) {
    console.error('Error rejecting protection request:', error);
    throw error;
  }
};

/**
 * Get pending protection cases
 */
const getPendingProtectionCases = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Pending protection cases endpoint - implementation pending' });
};

/**
 * Get protection case details
 */
const getProtectionCaseDetails = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Protection case details endpoint - implementation pending' });
};

module.exports = {
  submitProtectionRequest,
  getPendingProtectionRequests,
  approveProtectionRequest,
  rejectProtectionRequest,
  getPendingProtectionCases,
  getProtectionCaseDetails
};
