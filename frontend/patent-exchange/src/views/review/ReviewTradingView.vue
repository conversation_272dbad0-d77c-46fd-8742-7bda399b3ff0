<template>
  <div class="review-trading-view">
    <div class="container-fluid py-4">
      <div class="d-flex align-items-center mb-4">
        <i class="bi bi-clipboard-check text-primary me-3" style="font-size: 2rem;"></i>
        <div>
          <h2 class="mb-1">交易审核</h2>
          <p class="text-muted mb-0">审核专利交易申请</p>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <i class="bi bi-clock-history fs-1 me-3"></i>
                <div>
                  <h4 class="mb-0">{{ pendingTransactions.length }}</h4>
                  <small>待审核交易</small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <i class="bi bi-check-circle fs-1 me-3"></i>
                <div>
                  <h4 class="mb-0">{{ approvedCount }}</h4>
                  <small>已批准</small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-danger text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <i class="bi bi-x-circle fs-1 me-3"></i>
                <div>
                  <h4 class="mb-0">{{ rejectedCount }}</h4>
                  <small>已拒绝</small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <i class="bi bi-graph-up fs-1 me-3"></i>
                <div>
                  <h4 class="mb-0">{{ totalCount }}</h4>
                  <small>总交易数</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="text-muted mt-3">正在加载待审核交易...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Pending Transactions -->
      <div v-else-if="pendingTransactions.length > 0" class="row">
        <div v-for="transaction in pendingTransactions" :key="transaction.id" class="col-lg-6 mb-4">
          <div class="card shadow-sm h-100">
            <div class="card-header bg-warning text-dark">
              <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                  <i class="bi bi-file-earmark-text me-2"></i>
                  {{ transaction.patentName }}
                </h6>
                <span class="badge bg-dark">待审核</span>
              </div>
            </div>
            <div class="card-body">
              <!-- Patent Information -->
              <div class="row g-2 mb-3">
                <div class="col-6">
                  <small class="text-muted">专利号</small>
                  <div class="fw-bold small">{{ transaction.patentNumber }}</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">交易价格</small>
                  <div class="fw-bold text-success">¥{{ formatPrice(transaction.price) }}</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">买方</small>
                  <div>
                    <button
                      class="btn btn-link p-0 text-decoration-none small"
                      @click="showUserDetails(transaction.buyerAddress, transaction.buyerName)"
                    >
                      {{ transaction.buyerName }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </button>
                  </div>
                  <div class="text-muted small">{{ formatAddress(transaction.buyerAddress) }}</div>
                </div>
                <div class="col-6">
                  <small class="text-muted">卖方</small>
                  <div>
                    <button
                      class="btn btn-link p-0 text-decoration-none small"
                      @click="showUserDetails(transaction.sellerAddress, transaction.sellerName)"
                    >
                      {{ transaction.sellerName }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </button>
                  </div>
                  <div class="text-muted small">{{ formatAddress(transaction.sellerAddress) }}</div>
                </div>
                <div class="col-12">
                  <small class="text-muted">提交时间</small>
                  <div class="small">{{ formatDate(transaction.submitDate) }}</div>
                </div>
              </div>

              <!-- Patent Details Button -->
              <div class="mb-3">
                <button
                  class="btn btn-outline-info btn-sm w-100"
                  @click="showPatentDetails(transaction.patentId)"
                >
                  <i class="bi bi-eye me-2"></i>
                  查看专利详情
                </button>
              </div>

              <!-- Action Buttons -->
              <div class="d-flex gap-2">
                <button
                  class="btn btn-success btn-sm flex-fill"
                  @click="approveTransaction(transaction)"
                  :disabled="isProcessing"
                >
                  <span v-if="isProcessing && processingId === transaction.id" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-check-lg me-2"></i>
                  批准
                </button>
                <button
                  class="btn btn-danger btn-sm flex-fill"
                  @click="showRejectModal(transaction)"
                  :disabled="isProcessing"
                >
                  <i class="bi bi-x-lg me-2"></i>
                  拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-5">
        <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
        <h4 class="text-muted mt-3">暂无待审核交易</h4>
        <p class="text-muted">所有交易申请都已处理完毕</p>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title">
              <i class="bi bi-x-circle me-2"></i>
              拒绝交易申请
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div v-if="selectedTransaction">
              <h6>交易信息</h6>
              <div class="bg-light p-3 rounded mb-3">
                <div class="row g-2">
                  <div class="col-6">
                    <small class="text-muted">专利名称</small>
                    <div class="fw-bold">{{ selectedTransaction.patentName }}</div>
                  </div>
                  <div class="col-6">
                    <small class="text-muted">交易价格</small>
                    <div class="fw-bold text-success">¥{{ formatPrice(selectedTransaction.price) }}</div>
                  </div>
                </div>
              </div>

              <div class="mb-3">
                <label for="rejectReason" class="form-label">拒绝原因 <span class="text-danger">*</span></label>
                <textarea
                  class="form-control"
                  id="rejectReason"
                  rows="4"
                  v-model="rejectReason"
                  placeholder="请详细说明拒绝此交易申请的原因..."
                  :class="{ 'is-invalid': rejectReasonError }"
                ></textarea>
                <div v-if="rejectReasonError" class="invalid-feedback">
                  {{ rejectReasonError }}
                </div>
              </div>

              <!-- Quick Reason Templates -->
              <div class="mb-3">
                <label class="form-label">常用拒绝原因</label>
                <div class="d-flex flex-wrap gap-2">
                  <button
                    v-for="template in rejectTemplates"
                    :key="template"
                    class="btn btn-outline-secondary btn-sm"
                    @click="rejectReason = template"
                  >
                    {{ template }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="bi bi-x-lg me-2"></i>
              取消
            </button>
            <button
              type="button"
              class="btn btn-danger"
              @click="confirmReject"
              :disabled="isProcessing || !rejectReason.trim()"
            >
              <span v-if="isProcessing" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else class="bi bi-check-lg me-2"></i>
              确认拒绝
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { transactionService } from '@/services/transactionService'
import UserDetailModal from '@/components/UserDetailModal.vue'

export default {
  name: 'ReviewTradingView',
  components: {
    UserDetailModal
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()

    // State
    const isLoading = ref(false)
    const isProcessing = ref(false)
    const processingId = ref(null)
    const error = ref(null)
    const successMessage = ref(null)
    const pendingTransactions = ref([])
    const selectedTransaction = ref(null)
    const selectedUserAddress = ref(null)

    // Reject modal state
    const rejectReason = ref('')
    const rejectReasonError = ref(null)

    // Statistics
    const approvedCount = ref(0)
    const rejectedCount = ref(0)
    const totalCount = computed(() =>
      pendingTransactions.value.length + approvedCount.value + rejectedCount.value
    )

    // Reject reason templates
    const rejectTemplates = [
      '专利信息不完整或有误',
      '交易价格不合理',
      '买方资质不符合要求',
      '专利权存在争议',
      '相关文档缺失或无效',
      '违反平台交易规则'
    ]

    // Load pending transactions
    const loadPendingTransactions = async () => {
      try {
        isLoading.value = true
        error.value = null

        const transactions = await transactionService.getPendingTransactions()
        pendingTransactions.value = transactions

        // Load real statistics from backend
        try {
          const stats = await transactionService.getReviewStatistics()
          approvedCount.value = stats.transactions?.approved || 0
          rejectedCount.value = stats.transactions?.rejected || 0
        } catch (statsError) {
          console.warn('Failed to load statistics:', statsError)
          // Fallback to basic counts if statistics fail
          approvedCount.value = 0
          rejectedCount.value = 0
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    // Approve transaction
    const approveTransaction = async (transaction) => {
      try {
        isProcessing.value = true
        processingId.value = transaction.id

        const result = await transactionService.approveTransaction(
          transaction.id,
          authStore.account
        )

        if (result.success) {
          successMessage.value = `交易 ${transaction.patentName} 已批准`
          // Remove from pending list
          pendingTransactions.value = pendingTransactions.value.filter(
            t => t.id !== transaction.id
          )
          approvedCount.value++
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isProcessing.value = false
        processingId.value = null
      }
    }

    // Show reject modal
    const showRejectModal = (transaction) => {
      selectedTransaction.value = transaction
      rejectReason.value = ''
      rejectReasonError.value = null

      const modal = new bootstrap.Modal(document.getElementById('rejectModal'))
      modal.show()
    }

    // Confirm reject
    const confirmReject = async () => {
      try {
        if (!rejectReason.value.trim()) {
          rejectReasonError.value = '请输入拒绝原因'
          return
        }

        rejectReasonError.value = null
        isProcessing.value = true

        const result = await transactionService.rejectTransaction(
          selectedTransaction.value.id,
          authStore.account,
          rejectReason.value
        )

        if (result.success) {
          successMessage.value = `交易 ${selectedTransaction.value.patentName} 已拒绝`

          // Remove from pending list
          pendingTransactions.value = pendingTransactions.value.filter(
            t => t.id !== selectedTransaction.value.id
          )
          rejectedCount.value++

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('rejectModal'))
          modal.hide()
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isProcessing.value = false
      }
    }

    // Show patent details
    const showPatentDetails = (patentId) => {
      router.push({ name: 'patent-detail', params: { id: patentId } })
    }

    // Show user details modal
    const showUserDetails = (address, name) => {
      selectedUserAddress.value = address
      const modal = new bootstrap.Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    // Utility functions
    const formatPrice = (price) => {
      return new Intl.NumberFormat('zh-CN').format(price)
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const formatAddress = (address) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    // Initialize
    onMounted(() => {
      if (authStore.isConnected) {
        loadPendingTransactions()
      }
    })

    return {
      isLoading,
      isProcessing,
      processingId,
      error,
      successMessage,
      pendingTransactions,
      selectedTransaction,
      selectedUserAddress,
      rejectReason,
      rejectReasonError,
      rejectTemplates,
      approvedCount,
      rejectedCount,
      totalCount,
      approveTransaction,
      showRejectModal,
      confirmReject,
      showPatentDetails,
      showUserDetails,
      formatPrice,
      formatDate,
      formatAddress
    }
  }
}
</script>

<style scoped>
.review-trading-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.badge {
  border-radius: 6px;
}

.modal-content {
  border-radius: 12px;
}

.modal-header {
  border-radius: 12px 12px 0 0;
}
</style>
